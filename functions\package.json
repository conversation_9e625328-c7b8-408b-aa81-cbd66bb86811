{"name": "managementdoc-functions", "description": "Cloud Functions for Firebase - Document Management System", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"busboy": "^1.6.0", "cors": "^2.8.5", "express": "^4.19.2", "firebase-admin": "^12.5.0", "firebase-functions": "^6.0.1", "lodash": "^4.17.21", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "sharp": "^0.34.2", "uuid": "^10.0.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.7", "@types/multer": "^1.4.12", "@types/node": "^20.16.10", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0", "eslint": "^9.11.1", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.30.0", "firebase-functions-test": "^3.3.0", "jest": "^29.7.0", "ts-jest": "^29.2.5", "typescript": "^5.6.2"}, "private": true}