<p align="center">
  <a href="https://js-sdsl.org/" target="_blank" rel="noopener noreferrer">
    <img src="https://js-sdsl.org/assets/image/logo/logo-removebg.png" alt="js-sdsl logo" width="120" />
  </a>
</p>

<h3><p align="center">A javascript standard data structure library which benchmark against C++ STL</p></h3>

<p align="center">
  <a href="https://www.npmjs.com/package/js-sdsl"><img src="https://img.shields.io/npm/v/js-sdsl.svg" alt="NPM Version" /></a>
  <a href="https://github.com/js-sdsl/js-sdsl/actions/workflows/build.yml"><img src="https://img.shields.io/github/actions/workflow/status/js-sdsl/js-sdsl/build.yml" alt="Build Status" /></a>
  <a href='https://coveralls.io/github/js-sdsl/js-sdsl?branch=main'><img src='https://coveralls.io/repos/github/js-sdsl/js-sdsl/badge.svg?branch=main' alt='Coverage Status' /></a>
  <a href="https://github.com/js-sdsl/js-sdsl"><img src="https://img.shields.io/github/stars/js-sdsl/js-sdsl.svg" alt="GITHUB Star" /></a>
  <a href="https://npmcharts.com/compare/js-sdsl?minimal=true"><img src="https://img.shields.io/npm/dm/js-sdsl.svg" alt="NPM Downloads" /></a>
  <a href="https://unpkg.com/js-sdsl/dist/umd/js-sdsl.min.js"><img src="https://img.badgesize.io/https://unpkg.com/js-sdsl/dist/umd/js-sdsl.min.js?compression=gzip&style=flat-square/" alt="Gzip Size"></a>
  <a href="https://openbase.com/js/js-sdsl?utm_source=embedded&amp;utm_medium=badge&amp;utm_campaign=rate-badge"><img src="https://badges.openbase.com/js/rating/js-sdsl.svg?token=fh3LMNOV+JSWykSjtg1rA8kouSYkJoIDzGbvaByq5X0=" alt="Rate this package"/></a>
  <a href="https://opensource.org/licenses/MIT"><img src="https://img.shields.io/npm/l/js-sdsl.svg" alt="MIT-license" /></a>
  <a href="https://github.com/js-sdsl/js-sdsl/"><img src="https://img.shields.io/github/languages/top/js-sdsl/js-sdsl.svg" alt="GITHUB-language" /></a>
</p>

<p align="center">English | <a href="https://github.com/js-sdsl/js-sdsl/blob/main/README.zh-CN.md">简体中文</a></p>

## ✨ Included data structures

- **Stack** - first in last out stack.
- **Queue** - first in first out queue.
- **PriorityQueue** - heap-implemented priority queue.
- **Vector** - protected array, cannot to operate properties like `length` directly.
- **LinkList** - linked list of non-contiguous memory addresses.
- **Deque** - double-ended-queue, O(1) time complexity to `unshift` or getting elements by index.
- **OrderedSet** - sorted set which implemented by red black tree.
- **OrderedMap** - sorted map which implemented by red black tree.
- **HashSet** - refer to the [polyfill of ES6 Set](https://github.com/rousan/collections-es6).
- **HashMap** - refer to the [polyfill of ES6 Map](https://github.com/rousan/collections-es6).

## ⚔️ Benchmark

We are benchmarking against other popular data structure libraries. In some ways we're better than the best library. See [benchmark](https://js-sdsl.org/#/test/benchmark-analyze).

## 🖥 Supported platforms

| ![][Edge-Icon]<br/>IE / Edge | ![][Firefox-Icon]<br/>Firefox | ![][Chrome-Icon]<br/>Chrome | ![][Safari-Icon]<br/>Safari | ![][Opera-Icon]<br/>Opera | ![][NodeJs-Icon]<br/>NodeJs |
|:----------------------------:|:-----------------------------:|:---------------------------:|:---------------------------:|:-------------------------:|:---------------------------:|
|           Edge 12            |              36               |             49              |             10              |            36             |             10              |

## 📦 Download

Download directly by cdn:

- [js-sdsl.js](https://unpkg.com/js-sdsl/dist/umd/js-sdsl.js) (for development)
- [js-sdsl.min.js](https://unpkg.com/js-sdsl/dist/umd/js-sdsl.min.js) (for production)

Or install js-sdsl using npm:

```bash
npm install js-sdsl
```

Or you can download the isolation packages containing only the containers you want:

| package                                           | npm                                                                   | size                                                             | docs                        |
|---------------------------------------------------|-----------------------------------------------------------------------|------------------------------------------------------------------|-----------------------------|
| [@js-sdsl/stack][stack-package]                   | [![NPM Package][stack-npm-version]][stack-npm-link]                   | [![GZIP Size][stack-umd-size]][stack-umd-link]                   | [link][stack-docs]          |
| [@js-sdsl/queue][queue-package]                   | [![NPM Package][queue-npm-version]][queue-npm-link]                   | [![GZIP Size][queue-umd-size]][queue-umd-link]                   | [link][queue-docs]          |
| [@js-sdsl/priority-queue][priority-queue-package] | [![NPM Package][priority-queue-npm-version]][priority-queue-npm-link] | [![GZIP Size][priority-queue-umd-size]][priority-queue-umd-link] | [link][priority-queue-docs] |
| [@js-sdsl/vector][vector-package]                 | [![NPM Package][vector-npm-version]][vector-npm-link]                 | [![GZIP Size][vector-umd-size]][vector-umd-link]                 | [link][vector-docs]         |
| [@js-sdsl/link-list][link-list-package]           | [![NPM Package][link-list-npm-version]][link-list-npm-link]           | [![GZIP Size][link-list-umd-size]][link-list-umd-link]           | [link][link-list-docs]      |
| [@js-sdsl/deque][deque-package]                   | [![NPM Package][deque-npm-version]][deque-npm-link]                   | [![GZIP Size][deque-umd-size]][deque-umd-link]                   | [link][deque-docs]          |
| [@js-sdsl/ordered-set][ordered-set-package]       | [![NPM Package][ordered-set-npm-version]][ordered-set-npm-link]       | [![GZIP Size][ordered-set-umd-size]][ordered-set-umd-link]       | [link][ordered-set-docs]    |
| [@js-sdsl/ordered-map][ordered-map-package]       | [![NPM Package][ordered-map-npm-version]][ordered-map-npm-link]       | [![GZIP Size][ordered-map-umd-size]][ordered-map-umd-link]       | [link][ordered-map-docs]    |
| [@js-sdsl/hash-set][hash-set-package]             | [![NPM Package][hash-set-npm-version]][hash-set-npm-link]             | [![GZIP Size][hash-set-umd-size]][hash-set-umd-link]             | [link][hash-set-docs]       |
| [@js-sdsl/hash-map][hash-map-package]             | [![NPM Package][hash-map-npm-version]][hash-map-npm-link]             | [![GZIP Size][hash-map-umd-size]][hash-map-umd-link]             | [link][hash-map-docs]       |

## 🪒 Usage

You can visit our [official website](https://js-sdsl.org/) to get more information.

To help you have a better use, we also provide this [API document](https://js-sdsl.org/js-sdsl/index.html).

For previous versions of the documentation, please visit:

`https://js-sdsl.org/js-sdsl/previous/v${version}/index.html`

E.g.

[https://js-sdsl.org/js-sdsl/previous/v4.1.5/index.html](https://js-sdsl.org/js-sdsl/previous/v4.1.5/index.html)

### For browser

```html
<script src="https://unpkg.com/js-sdsl/dist/umd/js-sdsl.min.js"></script>
<script>
    const {
      Vector,
      Stack,
      Queue,
      LinkList,
      Deque,
      PriorityQueue,
      OrderedSet,
      OrderedMap,
      HashSet,
      HashMap
    } = sdsl;
    const myOrderedMap = new OrderedMap();
    myOrderedMap.setElement(1, 2);
    console.log(myOrderedMap.getElementByKey(1)); // 2
</script>
```

### For npm

```javascript
// esModule
import { OrderedMap } from 'js-sdsl';
// commonJs
const { OrderedMap } = require('js-sdsl');
const myOrderedMap = new OrderedMap();
myOrderedMap.setElement(1, 2);
console.log(myOrderedMap.getElementByKey(1)); // 2
```

## 🛠 Test

### Unit test

We use [karma](https://karma-runner.github.io/) and [mocha](https://mochajs.org/) frame to do unit tests and synchronize to [coveralls](https://coveralls.io/github/js-sdsl/js-sdsl). You can run `yarn test:unit` command to reproduce it.

### For performance

We tested most of the functions for efficiency. You can go to [`gh-pages/performance.md`](https://github.com/js-sdsl/js-sdsl/blob/gh-pages/performance.md) to see our running results or reproduce it with `yarn test:performance` command.

You can also visit [here](https://js-sdsl.org/#/test/performance-test) to get the result.

## ⌨️ Development

Use Gitpod, a free online dev environment for GitHub.

[![Open in Gippod](https://gitpod.io/button/open-in-gitpod.svg)](https://gitpod.io/#https://github.com/js-sdsl/js-sdsl)

Or clone locally:

```bash
$ git clone https://github.com/js-sdsl/js-sdsl.git
$ cd js-sdsl
$ npm install
$ npm run dev   # development mode
```

Then you can see the output in `dist/cjs` folder.

## 🤝 Contributing

Feel free to dive in! Open an issue or submit PRs. It may be helpful to read the [Contributor Guide](https://github.com/js-sdsl/js-sdsl/blob/main/.github/CONTRIBUTING.md).

### Contributors

Thanks goes to these wonderful people:

<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->
<!-- prettier-ignore-start -->
<!-- markdownlint-disable -->
<table>
  <tbody>
    <tr>
      <td align="center"><a href="https://www.linkedin.com/in/takatoshi-kondo-02a91410/"><img src="https://avatars.githubusercontent.com/u/275959?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Takatoshi Kondo</b></sub></a><br /><a href="https://github.com/js-sdsl/js-sdsl/commits?author=redboltz" title="Code">💻</a> <a href="https://github.com/js-sdsl/js-sdsl/commits?author=redboltz" title="Tests">⚠️</a></td>
      <td align="center"><a href="https://www.youtube.com/c/noname0310"><img src="https://avatars.githubusercontent.com/u/48761044?v=4?s=100" width="100px;" alt=""/><br /><sub><b>noname</b></sub></a><br /><a href="https://github.com/js-sdsl/js-sdsl/commits?author=noname0310" title="Code">💻</a></td>
    </tr>
  </tbody>
</table>

<!-- markdownlint-restore -->
<!-- prettier-ignore-end -->

<!-- ALL-CONTRIBUTORS-LIST:END -->

This project follows the [all-contributors](https://github.com/all-contributors/all-contributors) specification. Contributions of any kind welcome!

## ❤️ Sponsors and Backers

The special thanks to these sponsors or backers because they provided support at a very early stage:

<a href="https://eslint.org/"><img src="https://js-sdsl.org/assets/image/sponsors/eslint-logo-color.png" alt="eslint logo" width="150"></a>

Thanks also give to these sponsors or backers:

[![sponsors](https://opencollective.com/js-sdsl/tiers/sponsors.svg?avatarHeight=36)](https://opencollective.com/js-sdsl#support)

[![backers](https://opencollective.com/js-sdsl/tiers/backers.svg?avatarHeight=36)](https://opencollective.com/js-sdsl#support)

## 🪪 License

[MIT](https://github.com/js-sdsl/js-sdsl/blob/main/LICENSE) © [ZLY201](https://github.com/zly201)

[Edge-Icon]: https://js-sdsl.org/assets/image/platform/edge.png
[Firefox-Icon]: https://js-sdsl.org/assets/image/platform/firefox.png
[Chrome-Icon]: https://js-sdsl.org/assets/image/platform/chrome.png
[Safari-Icon]: https://js-sdsl.org/assets/image/platform/safari.png
[Opera-Icon]: https://js-sdsl.org/assets/image/platform/opera.png
[NodeJs-Icon]: https://js-sdsl.org/assets/image/platform/nodejs.png

[stack-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/OtherContainer/Stack.ts
[stack-npm-version]: https://img.shields.io/npm/v/@js-sdsl/stack
[stack-npm-link]: https://www.npmjs.com/package/@js-sdsl/stack
[stack-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/stack/dist/umd/stack.min.js?compression=gzip&style=flat-square/
[stack-umd-link]: https://unpkg.com/@js-sdsl/stack/dist/umd/stack.min.js
[stack-docs]: https://js-sdsl.org/js-sdsl/classes/Stack.html

[queue-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/OtherContainer/Queue.ts
[queue-npm-version]: https://img.shields.io/npm/v/@js-sdsl/queue
[queue-npm-link]: https://www.npmjs.com/package/@js-sdsl/queue
[queue-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/queue/dist/umd/queue.min.js?compression=gzip&style=flat-square/
[queue-umd-link]: https://unpkg.com/@js-sdsl/queue/dist/umd/queue.min.js
[queue-docs]: https://js-sdsl.org/js-sdsl/classes/Queue.html

[priority-queue-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/OtherContainer/PriorityQueue.ts
[priority-queue-npm-version]: https://img.shields.io/npm/v/@js-sdsl/priority-queue
[priority-queue-npm-link]: https://www.npmjs.com/package/@js-sdsl/priority-queue
[priority-queue-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/priority-queue/dist/umd/priority-queue.min.js?compression=gzip&style=flat-square/
[priority-queue-umd-link]: https://unpkg.com/@js-sdsl/priority-queue/dist/umd/priority-queue.min.js
[priority-queue-docs]: https://js-sdsl.org/js-sdsl/classes/PriorityQueue.html

[vector-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/SequentialContainer/Vector.ts
[vector-npm-version]: https://img.shields.io/npm/v/@js-sdsl/vector
[vector-npm-link]: https://www.npmjs.com/package/@js-sdsl/vector
[vector-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/vector/dist/umd/vector.min.js?compression=gzip&style=flat-square/
[vector-umd-link]: https://unpkg.com/@js-sdsl/vector/dist/umd/vector.min.js
[vector-docs]: https://js-sdsl.org/js-sdsl/classes/Vector.html

[link-list-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/SequentialContainer/LinkList.ts
[link-list-npm-version]: https://img.shields.io/npm/v/@js-sdsl/link-list
[link-list-npm-link]: https://www.npmjs.com/package/@js-sdsl/link-list
[link-list-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/link-list/dist/umd/link-list.min.js?compression=gzip&style=flat-square/
[link-list-umd-link]: https://unpkg.com/@js-sdsl/link-list/dist/umd/link-list.min.js
[link-list-docs]: https://js-sdsl.org/js-sdsl/classes/LinkList.html

[deque-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/SequentialContainer/Deque.ts
[deque-npm-version]: https://img.shields.io/npm/v/@js-sdsl/deque
[deque-npm-link]: https://www.npmjs.com/package/@js-sdsl/deque
[deque-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/deque/dist/umd/deque.min.js?compression=gzip&style=flat-square/
[deque-umd-link]: https://unpkg.com/@js-sdsl/deque/dist/umd/deque.min.js
[deque-docs]: https://js-sdsl.org/js-sdsl/classes/Deque.html

[ordered-set-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/TreeContainer/OrderedSet.ts
[ordered-set-npm-version]: https://img.shields.io/npm/v/@js-sdsl/ordered-set
[ordered-set-npm-link]: https://www.npmjs.com/package/@js-sdsl/ordered-set
[ordered-set-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/ordered-set/dist/umd/ordered-set.min.js?compression=gzip&style=flat-square/
[ordered-set-umd-link]: https://unpkg.com/@js-sdsl/ordered-set/dist/umd/ordered-set.min.js
[ordered-set-docs]: https://js-sdsl.org/js-sdsl/classes/OrderedSet.html

[ordered-map-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/TreeContainer/OrderedMap.ts
[ordered-map-npm-version]: https://img.shields.io/npm/v/@js-sdsl/ordered-map
[ordered-map-npm-link]: https://www.npmjs.com/package/@js-sdsl/ordered-map
[ordered-map-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/ordered-map/dist/umd/ordered-map.min.js?compression=gzip&style=flat-square/
[ordered-map-umd-link]: https://unpkg.com/@js-sdsl/ordered-map/dist/umd/ordered-map.min.js
[ordered-map-docs]: https://js-sdsl.org/js-sdsl/classes/OrderedMap.html

[hash-set-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/HashContainer/HashSet.ts
[hash-set-npm-version]: https://img.shields.io/npm/v/@js-sdsl/hash-set
[hash-set-npm-link]: https://www.npmjs.com/package/@js-sdsl/hash-set
[hash-set-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/hash-set/dist/umd/hash-set.min.js?compression=gzip&style=flat-square/
[hash-set-umd-link]: https://unpkg.com/@js-sdsl/hash-set/dist/umd/hash-set.min.js
[hash-set-docs]: https://js-sdsl.org/js-sdsl/classes/HashSet.html

[hash-map-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/HashContainer/HashMap.ts
[hash-map-npm-version]: https://img.shields.io/npm/v/@js-sdsl/hash-map
[hash-map-npm-link]: https://www.npmjs.com/package/@js-sdsl/hash-map
[hash-map-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/hash-map/dist/umd/hash-map.min.js?compression=gzip&style=flat-square/
[hash-map-umd-link]: https://unpkg.com/@js-sdsl/hash-map/dist/umd/hash-map.min.js
[hash-map-docs]: https://js-sdsl.org/js-sdsl/classes/HashMap.html
