<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Simplified configuration for release build compatibility -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <!-- Include system certificates -->
            <certificates src="system"/>
            <!-- Include user-added certificates for development -->
            <certificates src="user"/>
        </trust-anchors>
    </base-config>

    <!-- Development and debugging configuration -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">***********/24</domain>

        <!-- Firebase Emulator Suite -->
        <domain includeSubdomains="true">firebase-emulator-suite</domain>

        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </domain-config>
</network-security-config>
