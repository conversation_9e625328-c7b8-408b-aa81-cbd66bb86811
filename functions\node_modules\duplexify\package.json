{"name": "duplexify", "version": "4.1.3", "description": "Turn a writable and readable stream into a streams2 duplex stream with support for async initialization and streams1/streams2 input", "main": "index.js", "dependencies": {"end-of-stream": "^1.4.1", "inherits": "^2.0.3", "readable-stream": "^3.1.1", "stream-shift": "^1.0.2"}, "devDependencies": {"concat-stream": "^1.5.2", "tape": "^4.0.0", "through2": "^2.0.0"}, "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "git://github.com/mafintosh/duplexify"}, "keywords": ["duplex", "streams2", "streams", "stream", "writable", "readable", "async"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/duplexify/issues"}, "homepage": "https://github.com/mafintosh/duplexify"}