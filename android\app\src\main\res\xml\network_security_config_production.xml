<?xml version="1.0" encoding="utf-8"?>
<!-- Production Network Security Configuration with Certificate Pinning -->
<network-security-config>
    <!-- Firebase and Google Services Configuration -->
    <domain-config cleartextTrafficPermitted="false">
        <!-- Firebase Core Services -->
        <domain includeSubdomains="true">firebasestorage.googleapis.com</domain>
        <domain includeSubdomains="true">firebase.googleapis.com</domain>
        <domain includeSubdomains="true">firestore.googleapis.com</domain>
        <domain includeSubdomains="true">identitytoolkit.googleapis.com</domain>
        <domain includeSubdomains="true">securetoken.googleapis.com</domain>

        <!-- Google APIs and CDN -->
        <domain includeSubdomains="true">googleapis.com</domain>
        <domain includeSubdomains="true">google.com</domain>
        <domain includeSubdomains="true">gstatic.com</domain>
        <domain includeSubdomains="true">googleusercontent.com</domain>

        <!-- Trust anchors for Firebase services -->
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>

        <!-- Certificate pinning for production (use only when properly configured) -->
        <pin-set expiration="2026-12-31">
            <!-- Google Trust Services LLC certificate pin -->
            <pin digest="SHA-256">KwccWaCgrnaw6tsrrSO61FgLacNgG2MMLq8GE6+oP5I=</pin>
            <!-- GlobalSign Root CA certificate pin -->
            <pin digest="SHA-256">K87oWBWM9UZfyddvDfoxL+8lpNyoUB2ptGtn0fv6G2Q=</pin>
            <!-- Backup pin -->
            <pin digest="SHA-256">YLh1dUR9y6Kja30RrAn7JKnbQG/uEtLMkBgFF2Fuihg=</pin>
        </pin-set>
    </domain-config>

    <!-- Base configuration for all other domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
